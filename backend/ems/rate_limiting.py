"""
SECURITY FIX: Rate Limiting Implementation
Provides comprehensive rate limiting for API endpoints
"""

import time
import logging
from typing import Dict, Optional, Tuple
from django.core.cache import cache
from django.http import JsonResponse
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from functools import wraps
import hashlib

logger = logging.getLogger(__name__)


class RateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded"""
    def __init__(self, message: str, retry_after: int):
        self.message = message
        self.retry_after = retry_after
        super().__init__(message)


class RateLimiter:
    """
    SECURITY FIX: Comprehensive rate limiting implementation
    """
    
    def __init__(self):
        self.default_limits = {
            'login': {'requests': 5, 'window': 300},  # 5 attempts per 5 minutes
            'api': {'requests': 100, 'window': 60},   # 100 requests per minute
            'password_reset': {'requests': 3, 'window': 3600},  # 3 attempts per hour
            'registration': {'requests': 3, 'window': 3600},    # 3 registrations per hour
            'file_upload': {'requests': 10, 'window': 300},     # 10 uploads per 5 minutes
            'search': {'requests': 50, 'window': 60},           # 50 searches per minute
        }
    
    def get_client_identifier(self, request) -> str:
        """
        Generate unique identifier for client
        Uses IP address and User-Agent for better accuracy
        """
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')[:100]  # Limit length
        
        # Create hash of IP + User-Agent for privacy
        identifier_string = f"{ip_address}:{user_agent}"
        return hashlib.sha256(identifier_string.encode()).hexdigest()[:16]
    
    def get_client_ip(self, request) -> str:
        """Extract client IP address from request"""
        # Check for forwarded IP (behind proxy/load balancer)
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(',')[0].strip()
        
        # Check for real IP header
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip
        
        # Fallback to remote address
        return request.META.get('REMOTE_ADDR', '0.0.0.0')
    
    def get_cache_key(self, identifier: str, limit_type: str) -> str:
        """Generate cache key for rate limiting"""
        return f"rate_limit:{limit_type}:{identifier}"
    
    def is_rate_limited(self, request, limit_type: str = 'api') -> Tuple[bool, Optional[int]]:
        """
        Check if request should be rate limited
        Returns (is_limited, retry_after_seconds)
        """
        if not self.is_rate_limiting_enabled():
            return False, None
        
        client_id = self.get_client_identifier(request)
        cache_key = self.get_cache_key(client_id, limit_type)
        
        # Get rate limit configuration
        limit_config = self.default_limits.get(limit_type, self.default_limits['api'])
        max_requests = limit_config['requests']
        window_seconds = limit_config['window']
        
        # Get current request count and timestamp
        current_data = cache.get(cache_key, {'count': 0, 'window_start': time.time()})
        current_time = time.time()
        
        # Check if we're in a new window
        if current_time - current_data['window_start'] >= window_seconds:
            # Reset counter for new window
            current_data = {'count': 1, 'window_start': current_time}
            cache.set(cache_key, current_data, window_seconds)
            return False, None
        
        # Check if limit exceeded
        if current_data['count'] >= max_requests:
            retry_after = int(window_seconds - (current_time - current_data['window_start']))
            logger.warning(f"Rate limit exceeded for {client_id} on {limit_type}: "
                         f"{current_data['count']}/{max_requests} requests")
            return True, retry_after
        
        # Increment counter
        current_data['count'] += 1
        cache.set(cache_key, current_data, window_seconds)
        
        return False, None
    
    def is_rate_limiting_enabled(self) -> bool:
        """Check if rate limiting is enabled"""
        return getattr(settings, 'ENABLE_RATE_LIMITING', True)
    
    def get_remaining_requests(self, request, limit_type: str = 'api') -> Dict[str, int]:
        """Get remaining requests for client"""
        if not self.is_rate_limiting_enabled():
            return {'remaining': 999, 'reset_time': 0}
        
        client_id = self.get_client_identifier(request)
        cache_key = self.get_cache_key(client_id, limit_type)
        
        limit_config = self.default_limits.get(limit_type, self.default_limits['api'])
        max_requests = limit_config['requests']
        window_seconds = limit_config['window']
        
        current_data = cache.get(cache_key, {'count': 0, 'window_start': time.time()})
        current_time = time.time()
        
        # Check if in new window
        if current_time - current_data['window_start'] >= window_seconds:
            return {'remaining': max_requests - 1, 'reset_time': int(current_time + window_seconds)}
        
        remaining = max(0, max_requests - current_data['count'])
        reset_time = int(current_data['window_start'] + window_seconds)
        
        return {'remaining': remaining, 'reset_time': reset_time}


# Global rate limiter instance
rate_limiter = RateLimiter()


class RateLimitMiddleware:
    """
    SECURITY FIX: Rate limiting middleware
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limiter = rate_limiter
        
        # Define endpoint-specific rate limits
        self.endpoint_limits = {
            '/api/auth/login/': 'login',
            '/api/auth/register/': 'registration',
            '/api/auth/password-reset/': 'password_reset',
            '/api/upload/': 'file_upload',
            '/api/search/': 'search',
        }
    
    def __call__(self, request):
        # Determine rate limit type based on endpoint
        limit_type = self.get_limit_type(request.path)
        
        # Check rate limit
        is_limited, retry_after = self.rate_limiter.is_rate_limited(request, limit_type)
        
        if is_limited:
            return self.create_rate_limit_response(retry_after)
        
        # Process request
        response = self.get_response(request)
        
        # Add rate limit headers
        self.add_rate_limit_headers(response, request, limit_type)
        
        return response
    
    def get_limit_type(self, path: str) -> str:
        """Determine rate limit type based on request path"""
        for endpoint, limit_type in self.endpoint_limits.items():
            if path.startswith(endpoint):
                return limit_type
        return 'api'  # Default limit type
    
    def create_rate_limit_response(self, retry_after: int) -> JsonResponse:
        """Create rate limit exceeded response"""
        return JsonResponse({
            'error': 'Rate limit exceeded',
            'message': f'Too many requests. Try again in {retry_after} seconds.',
            'retry_after': retry_after,
            'code': 'RATE_LIMIT_EXCEEDED'
        }, status=429, headers={
            'Retry-After': str(retry_after),
            'X-RateLimit-Limit': '100',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': str(int(time.time()) + retry_after)
        })
    
    def add_rate_limit_headers(self, response, request, limit_type: str):
        """Add rate limiting headers to response"""
        try:
            rate_info = self.rate_limiter.get_remaining_requests(request, limit_type)
            limit_config = self.rate_limiter.default_limits.get(limit_type, 
                                                               self.rate_limiter.default_limits['api'])
            
            response['X-RateLimit-Limit'] = str(limit_config['requests'])
            response['X-RateLimit-Remaining'] = str(rate_info['remaining'])
            response['X-RateLimit-Reset'] = str(rate_info['reset_time'])
            response['X-RateLimit-Window'] = str(limit_config['window'])
            
        except Exception as e:
            logger.error(f"Error adding rate limit headers: {str(e)}")


def rate_limit(limit_type: str = 'api'):
    """
    SECURITY FIX: Decorator for rate limiting specific views
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            is_limited, retry_after = rate_limiter.is_rate_limited(request, limit_type)
            
            if is_limited:
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests. Try again in {retry_after} seconds.',
                    'retry_after': retry_after,
                    'code': 'RATE_LIMIT_EXCEEDED'
                }, status=429)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit_class_view(limit_type: str = 'api'):
    """
    SECURITY FIX: Class-based view decorator for rate limiting
    """
    def decorator(cls):
        original_dispatch = cls.dispatch
        
        @wraps(original_dispatch)
        def dispatch_wrapper(self, request, *args, **kwargs):
            is_limited, retry_after = rate_limiter.is_rate_limited(request, limit_type)
            
            if is_limited:
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests. Try again in {retry_after} seconds.',
                    'retry_after': retry_after,
                    'code': 'RATE_LIMIT_EXCEEDED'
                }, status=429)
            
            return original_dispatch(self, request, *args, **kwargs)
        
        cls.dispatch = dispatch_wrapper
        return cls
    
    return decorator


# SECURITY FIX: Suspicious activity tracking
class SuspiciousActivityTracker:
    """Track and respond to suspicious activity patterns"""
    
    def __init__(self):
        self.suspicious_thresholds = {
            'failed_logins': 10,      # 10 failed logins
            'invalid_requests': 20,    # 20 invalid requests
            'blocked_requests': 5,     # 5 blocked requests
        }
    
    def record_suspicious_activity(self, request, activity_type: str):
        """Record suspicious activity for monitoring"""
        client_id = rate_limiter.get_client_identifier(request)
        cache_key = f"suspicious:{activity_type}:{client_id}"
        
        # Increment counter
        current_count = cache.get(cache_key, 0) + 1
        cache.set(cache_key, current_count, 3600)  # 1 hour window
        
        # Check if threshold exceeded
        threshold = self.suspicious_thresholds.get(activity_type, 10)
        if current_count >= threshold:
            self.handle_suspicious_client(request, client_id, activity_type, current_count)
    
    def handle_suspicious_client(self, request, client_id: str, activity_type: str, count: int):
        """Handle client with suspicious activity"""
        logger.warning(f"Suspicious activity detected: {client_id} - {activity_type}: {count}")
        
        # Temporarily block client (extend rate limiting)
        block_key = f"blocked:{client_id}"
        cache.set(block_key, True, 1800)  # Block for 30 minutes
        
        # Log for security monitoring
        logger.critical(f"Client {client_id} temporarily blocked due to suspicious activity: {activity_type}")


# Global suspicious activity tracker
suspicious_activity_tracker = SuspiciousActivityTracker()
