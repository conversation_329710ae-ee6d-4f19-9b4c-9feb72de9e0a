"""
CSRF Token Views for Secure SPA Authentication
Provides secure CSRF token endpoints for frontend applications
"""

from django.http import JsonResponse
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import logging

logger = logging.getLogger(__name__)


@require_http_methods(["GET"])
@ensure_csrf_cookie
def get_csrf_token(request):
    """
    SECURITY FIX: Secure CSRF token endpoint for SPA
    Returns CSRF token in response body while setting httpOnly cookie
    """
    try:
        # Get CSRF token (this also sets the cookie)
        csrf_token = get_token(request)
        
        # Log the request for security monitoring
        logger.info(f"CSRF token requested from {request.META.get('REMOTE_ADDR')}")
        
        return JsonResponse({
            'csrfToken': csrf_token,
            'message': 'CSRF token generated successfully'
        })
    except Exception as e:
        logger.error(f"CSRF token generation failed: {str(e)}")
        return JsonResponse({
            'error': 'Failed to generate CSRF token'
        }, status=500)


class SecureCSRFView(View):
    """
    SECURITY FIX: Class-based view for CSRF token management
    Provides additional security features and logging
    """
    
    @method_decorator(ensure_csrf_cookie)
    def get(self, request):
        """Get CSRF token with enhanced security logging"""
        try:
            csrf_token = get_token(request)
            
            # Enhanced security logging
            client_ip = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
            
            logger.info(f"CSRF token requested - IP: {client_ip}, User-Agent: {user_agent[:100]}")
            
            # Check for suspicious patterns in request
            if self._is_suspicious_request(request):
                logger.warning(f"Suspicious CSRF token request from {client_ip}")
            
            return JsonResponse({
                'csrfToken': csrf_token,
                'message': 'CSRF token generated successfully',
                'timestamp': request.META.get('HTTP_DATE')
            })
            
        except Exception as e:
            logger.error(f"CSRF token generation failed: {str(e)}")
            return JsonResponse({
                'error': 'Failed to generate CSRF token'
            }, status=500)
    
    def _is_suspicious_request(self, request):
        """Check for suspicious patterns in CSRF token requests"""
        suspicious_patterns = [
            'bot', 'crawler', 'spider', 'scraper'
        ]
        
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        return any(pattern in user_agent for pattern in suspicious_patterns)


@require_http_methods(["POST"])
def validate_csrf_token(request):
    """
    SECURITY FIX: Endpoint to validate CSRF token
    Used for testing CSRF protection
    """
    try:
        # If we reach here, CSRF validation passed
        return JsonResponse({
            'valid': True,
            'message': 'CSRF token is valid'
        })
    except Exception as e:
        logger.error(f"CSRF validation failed: {str(e)}")
        return JsonResponse({
            'valid': False,
            'error': 'CSRF validation failed'
        }, status=403)
