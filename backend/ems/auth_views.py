"""
Authentication Views for EMS API
Handles JWT authentication, user registration, password reset, etc.
"""

from rest_framework import status, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
from django_ratelimit.decorators import ratelimit
from django_ratelimit.exceptions import Ratelimited
from datetime import timedelta
import logging

from .models import Employee, Role, UserProfile
from .serializers import UserSerializer, EmployeeSerializer, UserProfileSerializer
from .authentication import SecureTokenManager

logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """Serializer for login credentials"""
    username = serializers.CharField()
    password = serializers.CharField()


@method_decorator(ratelimit(key='ip', rate='5/m', method='POST', block=True), name='post')
class CustomTokenObtainPairView(APIView):
    """
    Custom JWT token obtain view that returns user data along with tokens
    Rate limited to 5 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            # Parse request data manually to handle both DRF and raw requests
            import json

            # Try to get data from different sources
            data = None
            if hasattr(request, 'data') and request.data:
                data = request.data
            elif request.content_type == 'application/json':
                try:
                    data = json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return Response({
                        'message': 'Invalid JSON data'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = request.POST

            if not data:
                return Response({
                    'message': 'No data provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Use serializer to validate input data
            serializer = LoginSerializer(data=data)
            if not serializer.is_valid():
                return Response({
                    'message': 'Username and password are required',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            username = serializer.validated_data['username']
            password = serializer.validated_data['password']

            # Authenticate user
            user = authenticate(username=username, password=password)

            if not user:
                return Response({
                    'message': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)

            if not user.is_active:
                return Response({
                    'message': 'User account is disabled'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Get user profile data
            user_data = self._get_user_data(user)

            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])

            # SECURITY FIX: Create response with httpOnly cookies
            response = Response({
                'user': user_data,
                'message': 'Login successful'
            }, status=status.HTTP_200_OK)

            # SECURITY FIX: Set httpOnly cookies for tokens
            # Get JWT settings safely
            jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
            access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))
            refresh_lifetime = jwt_settings.get('REFRESH_TOKEN_LIFETIME', timedelta(days=7))

            # Access token (short-lived)
            response.set_cookie(
                'access_token',
                str(access_token),
                max_age=int(access_lifetime.total_seconds()),
                httponly=True,
                secure=not settings.DEBUG,  # HTTPS only in production
                samesite='Lax'
            )

            # Refresh token (longer-lived)
            response.set_cookie(
                'refresh_token',
                str(refresh),
                max_age=int(refresh_lifetime.total_seconds()),
                httponly=True,
                secure=not settings.DEBUG,  # HTTPS only in production
                samesite='Lax'
            )

            return response

        except Ratelimited:
            return Response({
                'message': 'Too many login attempts. Please try again later.'
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response({
                'message': 'An error occurred during login'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_user_data(self, user):
        """Get comprehensive user data including profile and role information"""
        try:
            # Try to get employee profile
            employee = Employee.objects.select_related(
                'user', 'department', 'manager'
            ).get(user=user)

            # Get user profile for role information
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.get_name_display(),
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }
                else:
                    # Default role if role is None
                    role_data = {
                        'id': 'employee',
                        'name': 'Employee',
                        'nameAr': 'موظف',
                        'permissions': [],
                        'level': 5,
                        'dashboardConfig': {
                            'allowedRoutes': ['/dashboard', '/profile'],
                            'defaultWidgets': ['tasks', 'schedule'],
                            'customizations': {}
                        }
                    }
            except UserProfile.DoesNotExist:
                # Default role if no profile exists
                role_data = {
                    'id': 'employee',
                    'name': 'Employee',
                    'nameAr': 'موظف',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/dashboard', '/profile'],
                        'defaultWidgets': ['tasks', 'schedule'],
                        'customizations': {}
                    }
                }

            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': role_data,
                'profile': {
                    'avatar': None,  # Add avatar field to Employee model if needed
                    'phone': employee.phone,
                    'department': employee.department.name if employee.department else None,
                    'position': employee.position,
                    'preferred_language': 'ar',  # Default to Arabic
                    'timezone': 'Asia/Riyadh'
                }
            }

        except Employee.DoesNotExist:
            # User exists but no employee profile - check for UserProfile (for ADMIN, SUPERADMIN, etc.)
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.get_name_display(),
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }

                    return {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'role': role_data,
                        'profile': {
                            'avatar': None,
                            'phone': user_profile.phone,
                            'department': None,  # UserProfile doesn't have department
                            'position': None,    # UserProfile doesn't have position
                            'preferred_language': user_profile.preferred_language,
                            'timezone': user_profile.timezone
                        }
                    }

            except UserProfile.DoesNotExist:
                pass

            # Fallback for users with no profile at all
            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': {
                    'id': 'user',
                    'name': 'User',
                    'nameAr': 'مستخدم',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/profile'],
                        'defaultWidgets': [],
                        'customizations': {}
                    }
                },
                'profile': {
                    'avatar': None,
                    'phone': None,
                    'department': None,
                    'position': None,
                    'preferred_language': 'ar',
                    'timezone': 'Asia/Riyadh'
                }
            }

    def _get_allowed_routes(self, role_name):
        """Get allowed routes based on role"""
        route_map = {
            'SUPERADMIN': ['/admin/*', '/hr/*', '/finance/*', '/department/*', '/projects/*', '/employee/*'],
            'ADMIN': ['/admin/*'],
            'HR_MANAGER': ['/hr/*'],
            'FINANCE_MANAGER': ['/finance/*'],
            'DEPARTMENT_MANAGER': ['/department/*'],
            'PROJECT_MANAGER': ['/projects/*'],
            'EMPLOYEE': ['/employee/*']
        }
        return route_map.get(role_name, ['/employee/*'])

    def _get_default_widgets(self, role_name):
        """Get default widgets based on role"""
        widget_map = {
            'SUPERADMIN': ['system_overview', 'user_analytics', 'performance_metrics', 'security_dashboard', 'compliance_overview', 'ai_insights'],
            'ADMIN': ['system_overview', 'user_analytics', 'performance_metrics'],
            'HR_MANAGER': ['employee_stats', 'leave_requests', 'attendance_summary'],
            'FINANCE_MANAGER': ['budget_overview', 'expense_tracking', 'financial_reports'],
            'DEPARTMENT_MANAGER': ['team_overview', 'project_progress', 'department_metrics'],
            'PROJECT_MANAGER': ['project_status', 'task_overview', 'team_performance'],
            'EMPLOYEE': ['my_tasks', 'my_schedule', 'my_attendance']
        }
        return widget_map.get(role_name, ['my_tasks', 'my_schedule'])


# @method_decorator(ratelimit(key='ip', rate='10/m', method='POST', block=True), name='post')  # Temporarily disabled
class CustomTokenRefreshView(APIView):
    """
    SECURITY FIX: Custom token refresh view using httpOnly cookies
    Rate limited to 10 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if not refresh_token:
                return Response({
                    'message': 'Refresh token not found'
                }, status=status.HTTP_401_UNAUTHORIZED)

            try:
                # Validate and refresh the token
                refresh = RefreshToken(refresh_token)
                access_token = refresh.access_token

                # SECURITY FIX: Create response with new httpOnly cookies
                response = Response({
                    'message': 'Token refreshed successfully'
                }, status=status.HTTP_200_OK)

                # Set new access token cookie
                jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
                access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))

                response.set_cookie(
                    'access_token',
                    str(access_token),
                    max_age=int(access_lifetime.total_seconds()),
                    httponly=True,
                    secure=not settings.DEBUG,
                    samesite='Lax'
                )

                return response

            except TokenError as e:
                return Response({
                    'message': 'Invalid or expired refresh token'
                }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response({
                'message': 'An error occurred during token refresh'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogoutView(APIView):
    """
    SECURITY FIX: Logout view that clears httpOnly cookies and blacklists tokens
    """
    permission_classes = [permissions.AllowAny]  # Allow logout even if token is invalid

    def post(self, request):
        try:
            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if refresh_token:
                try:
                    token = RefreshToken(refresh_token)
                    # Only attempt to blacklist if blacklisting is enabled
                    jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
                    if jwt_settings.get('BLACKLIST_AFTER_ROTATION', False):
                        token.blacklist()
                    else:
                        logger.info("Token blacklisting is disabled, skipping blacklist operation")
                except Exception as blacklist_error:
                    # Log the blacklist error but don't fail the logout
                    logger.warning(f"Token blacklist failed (continuing with logout): {str(blacklist_error)}")

            # SECURITY FIX: Create response that clears httpOnly cookies
            response = Response({
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)

            # SECURITY FIX: Use SecureTokenManager for cookie cleanup
            response = SecureTokenManager.clear_auth_cookies(response)

            return response

        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            # Even on error, clear cookies
            response = Response({
                'message': 'Logged out (with errors)'
            }, status=status.HTTP_200_OK)

            response.delete_cookie('access_token', samesite='Lax')
            response.delete_cookie('refresh_token', samesite='Lax')

            return response


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile_view(request):
    """
    Get current authenticated user's profile
    """
    try:
        # Use the same method as login to get user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(request.user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"User profile error: {str(e)}")
        return Response({
            'message': 'An error occurred while fetching user profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile_view(request):
    """
    Update current user's profile
    """
    try:
        user = request.user
        data = request.data

        # Update User model fields
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'email' in data:
            user.email = data['email']

        user.save()

        # Update Employee model fields if exists
        try:
            employee = Employee.objects.get(user=user)
            if 'phone' in data.get('profile', {}):
                employee.phone = data['profile']['phone']
            employee.save()
        except Employee.DoesNotExist:
            pass

        # Return updated user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        return Response({
            'message': 'An error occurred while updating profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@ratelimit(key='user', rate='3/m', method='POST', block=True)
def change_password_view(request):
    """
    Change user password
    """
    try:
        user = request.user
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        if not current_password or not new_password:
            return Response({
                'message': 'Current password and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify current password
        if not user.check_password(current_password):
            return Response({
                'message': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        return Response({
            'message': 'An error occurred while changing password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# SECURITY FIX: CSRF Token Endpoint
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@ensure_csrf_cookie
def csrf_token_view(request):
    """
    SECURITY FIX: Provide CSRF token for frontend SPA
    This endpoint ensures the frontend can get a CSRF token
    """
    try:
        # Get CSRF token for the request
        csrf_token = get_token(request)

        return Response({
            'csrfToken': csrf_token,
            'message': 'CSRF token provided'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"CSRF token error: {str(e)}")
        return Response({
            'message': 'Failed to get CSRF token'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


