/**
 * SECURITY FIX: Content Sanitization Utilities
 * Prevents XSS attacks through proper content sanitization
 */

/**
 * SECURITY FIX: HTML sanitization patterns
 */
const DANGEROUS_HTML_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,
  /<iframe[^>]*>.*?<\/iframe>/gi,
  /<object[^>]*>.*?<\/object>/gi,
  /<embed[^>]*>.*?<\/embed>/gi,
  /<link[^>]*>/gi,
  /<meta[^>]*>/gi,
  /<style[^>]*>.*?<\/style>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /data:text\/html/gi,
  /on\w+\s*=/gi, // Event handlers like onclick, onload, etc.
]

const DANGEROUS_ATTRIBUTES = [
  'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
  'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
  'onselect', 'onkeydown', 'onkeyup', 'onkeypress'
]

/**
 * SECURITY FIX: Sanitize HTML content to prevent XSS
 */
export function sanitizeHTML(html: string): string {
  if (!html || typeof html !== 'string') {
    return ''
  }

  let sanitized = html

  // Remove dangerous HTML patterns
  DANGEROUS_HTML_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '')
  })

  // Remove dangerous attributes
  DANGEROUS_ATTRIBUTES.forEach(attr => {
    const attrPattern = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi')
    sanitized = sanitized.replace(attrPattern, '')
  })

  return sanitized
}

/**
 * SECURITY FIX: Escape HTML entities to prevent XSS
 */
export function escapeHTML(text: string): string {
  if (!text || typeof text !== 'string') {
    return ''
  }

  const entityMap: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  }

  return text.replace(/[&<>"'`=\/]/g, (char) => entityMap[char])
}

/**
 * SECURITY FIX: Sanitize user input for safe display
 */
export function sanitizeUserInput(input: string, options: {
  allowHTML?: boolean
  maxLength?: number
  stripWhitespace?: boolean
} = {}): string {
  if (!input || typeof input !== 'string') {
    return ''
  }

  let sanitized = input

  // Trim whitespace if requested
  if (options.stripWhitespace) {
    sanitized = sanitized.trim()
  }

  // Enforce maximum length
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength)
  }

  // Handle HTML content
  if (options.allowHTML) {
    sanitized = sanitizeHTML(sanitized)
  } else {
    sanitized = escapeHTML(sanitized)
  }

  return sanitized
}

/**
 * SECURITY FIX: Validate and sanitize URLs
 */
export function sanitizeURL(url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null
  }

  // Remove whitespace
  const cleanURL = url.trim()

  // Check for dangerous protocols
  const dangerousProtocols = ['javascript:', 'vbscript:', 'data:', 'file:']
  const lowerURL = cleanURL.toLowerCase()
  
  if (dangerousProtocols.some(protocol => lowerURL.startsWith(protocol))) {
    return null
  }

  // Only allow http, https, mailto, and tel protocols
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']
  const hasProtocol = allowedProtocols.some(protocol => lowerURL.startsWith(protocol))
  
  if (!hasProtocol && !cleanURL.startsWith('/') && !cleanURL.startsWith('#')) {
    // Assume relative URL, prepend with https://
    return `https://${cleanURL}`
  }

  return cleanURL
}

/**
 * SECURITY FIX: Sanitize CSS to prevent CSS injection
 */
export function sanitizeCSS(css: string): string {
  if (!css || typeof css !== 'string') {
    return ''
  }

  // Remove dangerous CSS patterns
  const dangerousPatterns = [
    /expression\s*\(/gi,
    /javascript\s*:/gi,
    /vbscript\s*:/gi,
    /@import/gi,
    /behavior\s*:/gi,
    /-moz-binding/gi,
    /url\s*\(\s*["']?\s*javascript:/gi,
    /url\s*\(\s*["']?\s*data:/gi
  ]

  let sanitized = css
  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '')
  })

  return sanitized
}

/**
 * SECURITY FIX: Content Security Policy helper
 */
export class ContentSecurityPolicy {
  private static nonce: string | null = null

  static generateNonce(): string {
    if (!ContentSecurityPolicy.nonce) {
      ContentSecurityPolicy.nonce = btoa(Math.random().toString()).substring(0, 16)
    }
    return ContentSecurityPolicy.nonce
  }

  static createSecureInlineStyle(css: string): string {
    const sanitizedCSS = sanitizeCSS(css)
    const nonce = ContentSecurityPolicy.generateNonce()
    return `<style nonce="${nonce}">${sanitizedCSS}</style>`
  }

  static isSecureURL(url: string): boolean {
    const sanitized = sanitizeURL(url)
    return sanitized !== null && (
      sanitized.startsWith('https://') ||
      sanitized.startsWith('/') ||
      sanitized.startsWith('#')
    )
  }
}

/**
 * SECURITY FIX: Safe DOM manipulation utilities
 */
export class SafeDOM {
  static setTextContent(element: Element, text: string): void {
    // Use textContent to prevent HTML injection
    element.textContent = text
  }

  static setInnerHTML(element: Element, html: string): void {
    // Sanitize HTML before setting
    element.innerHTML = sanitizeHTML(html)
  }

  static setAttribute(element: Element, name: string, value: string): void {
    // Prevent dangerous attributes
    if (DANGEROUS_ATTRIBUTES.includes(name.toLowerCase())) {
      console.warn(`Blocked dangerous attribute: ${name}`)
      return
    }

    // Sanitize URL attributes
    if (['href', 'src', 'action'].includes(name.toLowerCase())) {
      const sanitizedURL = sanitizeURL(value)
      if (sanitizedURL) {
        element.setAttribute(name, sanitizedURL)
      }
      return
    }

    // Sanitize other attributes
    element.setAttribute(name, escapeHTML(value))
  }

  static createElement(tagName: string, attributes?: Record<string, string>, textContent?: string): Element {
    const element = document.createElement(tagName)

    if (attributes) {
      Object.entries(attributes).forEach(([name, value]) => {
        SafeDOM.setAttribute(element, name, value)
      })
    }

    if (textContent) {
      SafeDOM.setTextContent(element, textContent)
    }

    return element
  }
}

/**
 * SECURITY FIX: React component sanitization helper
 */
export function createSafeProps(props: Record<string, any>): Record<string, any> {
  const safeProps: Record<string, any> = {}

  Object.entries(props).forEach(([key, value]) => {
    // Skip dangerous props
    if (DANGEROUS_ATTRIBUTES.includes(key.toLowerCase())) {
      console.warn(`Blocked dangerous prop: ${key}`)
      return
    }

    // Sanitize string values
    if (typeof value === 'string') {
      if (key === 'href' || key === 'src') {
        const sanitizedURL = sanitizeURL(value)
        if (sanitizedURL) {
          safeProps[key] = sanitizedURL
        }
      } else if (key === 'dangerouslySetInnerHTML') {
        // Never allow dangerouslySetInnerHTML without sanitization
        console.warn('Blocked dangerouslySetInnerHTML - use sanitized content instead')
        return
      } else {
        safeProps[key] = sanitizeUserInput(value)
      }
    } else {
      safeProps[key] = value
    }
  })

  return safeProps
}

/**
 * SECURITY FIX: Validation utilities
 */
export const SecurityValidators = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && !email.includes('<') && !email.includes('>')
  },

  isValidURL: (url: string): boolean => {
    try {
      const parsed = new URL(url)
      return ['http:', 'https:'].includes(parsed.protocol)
    } catch {
      return false
    }
  },

  isValidPhoneNumber: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/
    return phoneRegex.test(phone) && phone.length >= 10 && phone.length <= 20
  },

  containsSuspiciousContent: (content: string): boolean => {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ]

    return suspiciousPatterns.some(pattern => pattern.test(content))
  }
}
