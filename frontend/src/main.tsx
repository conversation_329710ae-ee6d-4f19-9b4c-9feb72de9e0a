import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { store } from './store'
import './index.css'
import App from './App.tsx'

// SECURITY FIX: Initialize CSRF protection
import { initializeCSRFProtection } from './utils/csrfToken'

// MOBILE FIX: Initialize mobile optimization
import { mobileOptimization } from './utils/mobileOptimization'

// PERFORMANCE FIX: Initialize performance optimizations
import { BundleOptimizer, ResourceMonitor } from './utils/finalPerformanceOptimizations'
import { performanceValidator } from './utils/performanceValidator'

// Initialize CSRF protection on app startup
initializeCSRFProtection()

// Initialize mobile optimization
console.log('📱 Mobile optimization initialized:', mobileOptimization.getDeviceInfo())

// PERFORMANCE FIX: Initialize performance monitoring and optimizations
console.log('⚡ Performance optimizations initialized')

// PERFORMANCE FIX: Preload actual critical resources, not API endpoints
BundleOptimizer.preloadResource('/assets/fonts/inter-var.woff2', 'font')
BundleOptimizer.preloadResource('/assets/critical.css', 'style')

// PERFORMANCE FIX: Prefetch critical API data instead of preloading as scripts
const prefetchCriticalData = async () => {
  try {
    // Only prefetch if user might be authenticated (has cookies)
    if (document.cookie.includes('access_token')) {
      // Prefetch user data
      fetch('/api/auth/user/', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' }
      }).catch(() => {}) // Silent fail for prefetch

      // Prefetch departments data
      fetch('/api/departments/', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' }
      }).catch(() => {}) // Silent fail for prefetch
    }
  } catch (error) {
    // Silent fail for prefetch operations
  }
}

// Start prefetching after a short delay to not block initial render
setTimeout(prefetchCriticalData, 1000)

// Run performance validation in development
if (process.env.NODE_ENV === 'development') {
  setTimeout(async () => {
    const result = await performanceValidator.validatePerformance()
    console.log('🎯 Performance Validation Result:', result.grade, `(${result.score}/100)`)
    if (result.issues.length > 0) {
      console.warn('⚠️ Performance Issues:', result.issues)
    }
  }, 5000) // Wait 5 seconds for app to fully load
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </StrictMode>,
)
