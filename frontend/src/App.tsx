import { useState, useEffect, useRef } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Toaster } from 'react-hot-toast'
import type { AppDispatch, RootState } from './store'
import { verifyToken } from './store/slices/authSlice'
import { fetchNotifications } from './store/slices/notificationSlice'
import { fetchDashboardLayout } from './store/slices/dashboardSlice'
import Layout from './components/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import Login from './pages/Login'
import Home from './pages/Home'
import HowItWorks from './pages/HowItWorks'
import RoleBasedRouter from './routes/RoleBasedRouter'
import { ModalProvider } from './contexts/ModalContext'
import ModalManager from './components/modals/ModalManager'
import { getPerformanceMonitor } from './utils/performanceMonitor'
import { testLodashImports } from './utils/testLodash'
// Removed unused imports - components and test utilities cleaned up





function App() {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user, isLoading, token } = useSelector((state: RootState) => state.auth)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [hasTriedTokenVerification, setHasTriedTokenVerification] = useState(false)

  // FIXED: Set document direction based on language (memory leak prevention)
  useEffect(() => {
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = language

    // FIXED: Use CSS class toggle instead of forced reflow (prevents memory leak)
    document.documentElement.classList.toggle('rtl', language === 'ar')
    document.documentElement.classList.toggle('ltr', language === 'en')

    // FIXED: Use CSS custom property instead of cache busting
    document.documentElement.style.setProperty('--text-direction', language === 'ar' ? 'rtl' : 'ltr')

    // REMOVED: Aggressive cache busting and forced reflow that caused memory leaks
    // The previous implementation blocked the main thread and caused memory accumulation
  }, [language])

  // FIXED: Proper async state management for token verification
  const [verificationState, setVerificationState] = useState<'idle' | 'pending' | 'complete' | 'failed'>('idle')

  useEffect(() => {
    // Only start verification if we haven't tried yet
    if (verificationState !== 'idle') return

    console.log('🔍 Starting token verification via httpOnly cookies...')
    setVerificationState('pending')

    dispatch(verifyToken())
      .unwrap()
      .then(() => {
        console.log('✅ Token verification successful')
        setVerificationState('complete')
      })
      .catch((error) => {
        console.error('❌ Token verification failed:', error)
        setVerificationState('failed')
        // No localStorage cleanup needed - tokens are in httpOnly cookies
      })
  }, [dispatch, verificationState])

  // Update hasTriedTokenVerification based on verification state
  useEffect(() => {
    if (verificationState === 'complete' || verificationState === 'failed') {
      setHasTriedTokenVerification(true)
    }
  }, [verificationState])

  // REMOVED: All timeout and reload logic that was causing issues
  // The app should handle authentication states naturally without forced reloads

  // Load user-specific data when authenticated (with deduplication)
  useEffect(() => {
    if (isAuthenticated && user?.role?.id) {
      // Check if we've recently fetched data to prevent duplicates
      const lastFetch = sessionStorage.getItem('app_data_last_fetch')
      const now = Date.now()
      const shouldFetch = !lastFetch || (now - parseInt(lastFetch)) > 30000 // 30 seconds

      if (shouldFetch) {
        console.log('🚀 App: Loading user-specific data')
        dispatch(fetchNotifications(user.role.id))
        dispatch(fetchDashboardLayout(user.role.id))
        // SECURITY FIX: Use sessionStorage for non-sensitive data instead of localStorage
        sessionStorage.setItem('app_data_last_fetch', now.toString())
      } else {
        console.log('🔄 App: Skipping data fetch - recent data available')
      }
    }
  }, [dispatch, isAuthenticated, user?.role?.id]) // FIXED: More specific dependency

  // Initialize performance monitoring
  useEffect(() => {
    const monitor = getPerformanceMonitor()

    // Send metrics to analytics after page load
    const timer = setTimeout(() => {
      monitor.sendMetricsToAnalytics()
    }, 3000)

    // Test lodash imports to verify module resolution
    if (process.env.NODE_ENV === 'development') {
      testLodashImports()
    }

    return () => clearTimeout(timer)
  }, [])

  // Removed export test functions - cleaned up debugging code

  // Debug logging
  console.log('App State:', {
    isLoading,
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    hasTriedTokenVerification
  })

  // REMOVED: Inconsistent state logic that was clearing valid tokens
  // Let the natural auth flow handle state consistency

  // Show loading screen only when actively verifying token
  if (verificationState === 'pending' || (isLoading && verificationState === 'idle')) {
    console.log('App: Showing loading screen for token verification')
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center glass-card p-8 rounded-xl shadow-xl">
          <div className="animate-spin rounded-full h-24 w-24 border-b-2 border-white mx-auto mb-6"></div>
          <p className="text-white text-xl mb-4 font-medium">
            {language === 'ar' ? 'جاري تحميل بيانات المستخدم...' : 'Loading user data...'}
          </p>
          <p className="text-white/70 text-sm">
            {language === 'ar' ? 'التحقق من المصادقة...' : 'Verifying authentication...'}
          </p>
          {/* Loading progress bar */}
          <div className="w-full h-1 bg-white/10 rounded-full mt-6 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"
                 style={{width: '60%'}}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <ModalProvider>
        <Router>
          {!isAuthenticated ? (
            <Routes>
              <Route path="/login" element={<Login language={language} />} />
              <Route path="/how-it-works" element={<HowItWorks language={language} setLanguage={setLanguage} />} />
              <Route path="/home" element={<Home language={language} setLanguage={setLanguage} />} />
              {/* Redirect all other routes to login for unauthenticated users */}
              <Route path="/*" element={<Login language={language} />} />
            </Routes>
          ) : (
            <Layout language={language} setLanguage={setLanguage}>
              <RoleBasedRouter language={language} />
              {/* Modal Manager for centralized modal handling */}
              <ModalManager language={language} />
            </Layout>
          )}

          {/* Chat widgets removed - cleaned up unused components */}
        </Router>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
          style: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: '#fff',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '500'
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
      </ModalProvider>
    </ErrorBoundary>
  )
}

export default App
