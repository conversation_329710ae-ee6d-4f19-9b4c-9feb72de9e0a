/**
 * CRITICAL SECURITY VALIDATOR
 * Real-time security validation tool to verify all fixes are working
 */

interface SecurityCheck {
  name: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  fix?: string
}

interface SecurityReport {
  timestamp: Date
  overallStatus: 'secure' | 'vulnerable' | 'warning'
  checks: SecurityCheck[]
  score: number
}

export class SecurityValidator {
  private checks: SecurityCheck[] = []

  async runAllChecks(): Promise<SecurityReport> {
    this.checks = []
    
    console.log('🔒 Starting comprehensive security validation...')
    
    // Run all security checks
    await this.checkTokenStorage()
    await this.checkAPIAuthentication()
    await this.checkWebSocketSecurity()
    await this.checkXSSProtection()
    await this.checkCORSConfiguration()
    await this.checkInputValidation()
    await this.checkNetworkSecurity()
    
    const report = this.generateReport()
    this.displayReport(report)
    
    return report
  }

  private async checkTokenStorage(): Promise<void> {
    console.log('🔍 Checking JWT token storage security...')
    
    // Check localStorage
    const localStorageKeys = Object.keys(localStorage)
    const localTokenKeys = localStorageKeys.filter(key => 
      key.toLowerCase().includes('token') || 
      key.toLowerCase().includes('jwt') ||
      key.toLowerCase().includes('auth')
    )
    
    if (localTokenKeys.length === 0) {
      this.addCheck({
        name: 'localStorage Token Security',
        status: 'pass',
        message: 'No JWT tokens found in localStorage',
        severity: 'critical'
      })
    } else {
      this.addCheck({
        name: 'localStorage Token Security',
        status: 'fail',
        message: `Found ${localTokenKeys.length} token-related keys in localStorage: ${localTokenKeys.join(', ')}`,
        severity: 'critical',
        fix: 'Remove all token storage from localStorage and use httpOnly cookies'
      })
    }

    // Check sessionStorage
    const sessionStorageKeys = Object.keys(sessionStorage)
    const sessionTokenKeys = sessionStorageKeys.filter(key => 
      key.toLowerCase().includes('token') || 
      key.toLowerCase().includes('jwt')
    )
    
    if (sessionTokenKeys.length === 0) {
      this.addCheck({
        name: 'sessionStorage Token Security',
        status: 'pass',
        message: 'No JWT tokens found in sessionStorage',
        severity: 'critical'
      })
    } else {
      this.addCheck({
        name: 'sessionStorage Token Security',
        status: 'fail',
        message: `Found ${sessionTokenKeys.length} token-related keys in sessionStorage`,
        severity: 'critical',
        fix: 'Remove all token storage from sessionStorage'
      })
    }
  }

  private async checkAPIAuthentication(): Promise<void> {
    console.log('🔍 Checking API authentication security...')
    
    try {
      // Test API call to see if it uses proper authentication
      const response = await fetch('/api/auth/user/', {
        credentials: 'include'
      })
      
      if (response.ok) {
        this.addCheck({
          name: 'API Authentication Method',
          status: 'pass',
          message: 'API calls successfully use httpOnly cookie authentication',
          severity: 'critical'
        })
      } else if (response.status === 401) {
        this.addCheck({
          name: 'API Authentication Method',
          status: 'warning',
          message: 'API authentication working (401 expected when not logged in)',
          severity: 'medium'
        })
      }
    } catch (error) {
      this.addCheck({
        name: 'API Authentication Method',
        status: 'warning',
        message: 'Could not test API authentication (network error)',
        severity: 'medium'
      })
    }
  }

  private async checkWebSocketSecurity(): Promise<void> {
    console.log('🔍 Checking WebSocket security...')
    
    // Check if WebSocket service exists and uses secure connection
    try {
      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'
      
      // Check if URL contains token parameter (should not)
      if (wsUrl.includes('token=')) {
        this.addCheck({
          name: 'WebSocket Token Security',
          status: 'fail',
          message: 'WebSocket URL contains token parameter',
          severity: 'critical',
          fix: 'Remove token from WebSocket URL and use cookie authentication'
        })
      } else {
        this.addCheck({
          name: 'WebSocket Token Security',
          status: 'pass',
          message: 'WebSocket does not expose tokens in URL',
          severity: 'critical'
        })
      }

      // Check if using secure WebSocket in production
      if (window.location.protocol === 'https:' && wsUrl.startsWith('ws:')) {
        this.addCheck({
          name: 'WebSocket Protocol Security',
          status: 'fail',
          message: 'Using insecure WebSocket (ws://) on HTTPS site',
          severity: 'high',
          fix: 'Use secure WebSocket (wss://) for HTTPS sites'
        })
      } else {
        this.addCheck({
          name: 'WebSocket Protocol Security',
          status: 'pass',
          message: 'WebSocket protocol matches site security',
          severity: 'high'
        })
      }
    } catch (error) {
      this.addCheck({
        name: 'WebSocket Security',
        status: 'warning',
        message: 'Could not validate WebSocket security',
        severity: 'medium'
      })
    }
  }

  private async checkXSSProtection(): Promise<void> {
    console.log('🔍 Checking XSS protection...')
    
    // SECURITY FIX: Test if tokens are accessible via JavaScript (safe method)
    try {
      
      // SECURITY FIX: Use safe function instead of eval()
      const testTokenAccess = (): string[] => {
        const tokens: string[] = []

        try {
          const localToken = localStorage.getItem('access_token')
          if (localToken) tokens.push('localStorage')
        } catch (e) {
          // Access blocked - good for security
        }

        try {
          const sessionToken = sessionStorage.getItem('access_token')
          if (sessionToken) tokens.push('sessionStorage')
        } catch (e) {
          // Access blocked - good for security
        }

        try {
          const cookieTokens = document.cookie.match(/access_token=([^;]+)/)
          if (cookieTokens) tokens.push('cookies')
        } catch (e) {
          // Access blocked - good for security
        }

        return tokens
      }

      const accessibleTokens = testTokenAccess()
      
      if (accessibleTokens.length === 0) {
        this.addCheck({
          name: 'XSS Token Protection',
          status: 'pass',
          message: 'Tokens are not accessible via JavaScript (XSS protected)',
          severity: 'critical'
        })
      } else {
        this.addCheck({
          name: 'XSS Token Protection',
          status: 'fail',
          message: `Tokens accessible via: ${accessibleTokens.join(', ')}`,
          severity: 'critical',
          fix: 'Use httpOnly cookies for all authentication tokens'
        })
      }
    } catch (error) {
      this.addCheck({
        name: 'XSS Token Protection',
        status: 'pass',
        message: 'Script execution blocked (good security)',
        severity: 'critical'
      })
    }
  }

  private async checkCORSConfiguration(): Promise<void> {
    console.log('🔍 Checking CORS configuration...')
    
    // This would typically be tested on the backend
    this.addCheck({
      name: 'CORS Configuration',
      status: 'warning',
      message: 'CORS configuration should be validated on backend',
      severity: 'high',
      fix: 'Ensure CORS_ALLOW_ALL_ORIGINS is False in production'
    })
  }

  private async checkInputValidation(): Promise<void> {
    console.log('🔍 Checking input validation...')
    
    // Test basic input validation patterns
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      '../../../etc/passwd',
      '{{7*7}}'
    ]
    
    // This is a basic check - real validation happens on backend
    this.addCheck({
      name: 'Input Validation',
      status: 'warning',
      message: 'Input validation should be tested with backend integration',
      severity: 'high',
      fix: 'Implement comprehensive input sanitization on backend'
    })
  }

  private async checkNetworkSecurity(): Promise<void> {
    console.log('🔍 Checking network security...')
    
    // Check if site is using HTTPS
    if (window.location.protocol === 'https:') {
      this.addCheck({
        name: 'HTTPS Usage',
        status: 'pass',
        message: 'Site is using HTTPS',
        severity: 'high'
      })
    } else {
      this.addCheck({
        name: 'HTTPS Usage',
        status: 'fail',
        message: 'Site is not using HTTPS',
        severity: 'high',
        fix: 'Enable HTTPS for all production traffic'
      })
    }

    // Check Content Security Policy
    const csp = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
    if (csp) {
      this.addCheck({
        name: 'Content Security Policy',
        status: 'pass',
        message: 'CSP header detected',
        severity: 'medium'
      })
    } else {
      this.addCheck({
        name: 'Content Security Policy',
        status: 'warning',
        message: 'No CSP header detected',
        severity: 'medium',
        fix: 'Implement Content Security Policy headers'
      })
    }
  }

  private addCheck(check: SecurityCheck): void {
    this.checks.push(check)
  }

  private generateReport(): SecurityReport {
    const criticalFails = this.checks.filter(c => c.severity === 'critical' && c.status === 'fail').length
    const highFails = this.checks.filter(c => c.severity === 'high' && c.status === 'fail').length
    const totalChecks = this.checks.length
    const passedChecks = this.checks.filter(c => c.status === 'pass').length
    
    let overallStatus: 'secure' | 'vulnerable' | 'warning'
    let score: number
    
    if (criticalFails > 0) {
      overallStatus = 'vulnerable'
      score = Math.max(0, 100 - (criticalFails * 30) - (highFails * 15))
    } else if (highFails > 0) {
      overallStatus = 'warning'
      score = Math.max(50, 100 - (highFails * 15))
    } else {
      overallStatus = 'secure'
      score = Math.round((passedChecks / totalChecks) * 100)
    }
    
    return {
      timestamp: new Date(),
      overallStatus,
      checks: this.checks,
      score
    }
  }

  private displayReport(report: SecurityReport): void {
    console.log('\n🔒 SECURITY VALIDATION REPORT')
    console.log('================================')
    console.log(`Overall Status: ${report.overallStatus.toUpperCase()}`)
    console.log(`Security Score: ${report.score}/100`)
    console.log(`Timestamp: ${report.timestamp.toISOString()}`)
    console.log('\nDetailed Results:')
    
    report.checks.forEach(check => {
      const icon = check.status === 'pass' ? '✅' : check.status === 'fail' ? '❌' : '⚠️'
      console.log(`${icon} ${check.name}: ${check.message}`)
      if (check.fix) {
        console.log(`   🔧 Fix: ${check.fix}`)
      }
    })
    
    console.log('\n================================')
  }
}

// Export singleton instance
export const securityValidator = new SecurityValidator()

// Auto-run in development
if (import.meta.env.DEV) {
  // Run security check after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      securityValidator.runAllChecks()
    }, 2000)
  })
}
