/**
 * ARCHITECTURE FIX: Standardized Error Handling System
 * Provides consistent error handling patterns across the application
 */

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NETWORK = 'network',
  VALIDATION = 'validation',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  USER_INPUT = 'user_input'
}

export interface StandardError {
  id: string
  message: string
  code: string
  severity: ErrorSeverity
  category: ErrorCategory
  timestamp: Date
  context?: Record<string, any>
  stack?: string
  userMessage?: string
  actionable?: boolean
  retryable?: boolean
}

export class AuthenticationError extends Error {
  public readonly code = 'AUTH_ERROR'
  public readonly severity = ErrorSeverity.HIGH
  public readonly category = ErrorCategory.AUTHENTICATION
  public readonly retryable = false

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class ValidationError extends Error {
  public readonly code = 'VALIDATION_ERROR'
  public readonly severity = ErrorSeverity.MEDIUM
  public readonly category = ErrorCategory.VALIDATION
  public readonly retryable = false

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class NetworkError extends Error {
  public readonly code = 'NETWORK_ERROR'
  public readonly severity = ErrorSeverity.HIGH
  public readonly category = ErrorCategory.NETWORK
  public readonly retryable = true

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class BusinessLogicError extends Error {
  public readonly code = 'BUSINESS_ERROR'
  public readonly severity = ErrorSeverity.MEDIUM
  public readonly category = ErrorCategory.BUSINESS_LOGIC
  public readonly retryable = false

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message)
    this.name = 'BusinessLogicError'
  }
}

export class StandardizedErrorHandler {
  private static instance: StandardizedErrorHandler
  private errorLog: StandardError[] = []
  private maxLogSize = 1000

  static getInstance(): StandardizedErrorHandler {
    if (!StandardizedErrorHandler.instance) {
      StandardizedErrorHandler.instance = new StandardizedErrorHandler()
    }
    return StandardizedErrorHandler.instance
  }

  /**
   * ARCHITECTURE FIX: Standardized error processing
   */
  handleError(error: Error | StandardError, context?: Record<string, any>): StandardError {
    const standardError = this.normalizeError(error, context)
    
    // Log the error
    this.logError(standardError)
    
    // Report to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(standardError)
    }
    
    // Show user-friendly message if needed
    if (standardError.actionable) {
      this.showUserMessage(standardError)
    }
    
    return standardError
  }

  private normalizeError(error: Error | StandardError, context?: Record<string, any>): StandardError {
    // If already a StandardError, return as-is
    if ('severity' in error && 'category' in error) {
      return error as StandardError
    }

    // Convert regular Error to StandardError
    const baseError = error as Error
    
    return {
      id: this.generateErrorId(),
      message: baseError.message,
      code: this.determineErrorCode(baseError),
      severity: this.determineSeverity(baseError),
      category: this.determineCategory(baseError),
      timestamp: new Date(),
      context: { ...context, originalError: baseError.name },
      stack: baseError.stack,
      userMessage: this.generateUserMessage(baseError),
      actionable: this.isActionable(baseError),
      retryable: this.isRetryable(baseError)
    }
  }

  private determineErrorCode(error: Error): string {
    if (error.name === 'TypeError') return 'TYPE_ERROR'
    if (error.name === 'ReferenceError') return 'REFERENCE_ERROR'
    if (error.message.includes('fetch')) return 'NETWORK_ERROR'
    if (error.message.includes('401')) return 'AUTH_ERROR'
    if (error.message.includes('403')) return 'PERMISSION_ERROR'
    if (error.message.includes('404')) return 'NOT_FOUND_ERROR'
    if (error.message.includes('500')) return 'SERVER_ERROR'
    return 'UNKNOWN_ERROR'
  }

  private determineSeverity(error: Error): ErrorSeverity {
    if (error.message.includes('401') || error.message.includes('403')) {
      return ErrorSeverity.HIGH
    }
    if (error.message.includes('500')) {
      return ErrorSeverity.CRITICAL
    }
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return ErrorSeverity.HIGH
    }
    return ErrorSeverity.MEDIUM
  }

  private determineCategory(error: Error): ErrorCategory {
    if (error.message.includes('401') || error.message.includes('403')) {
      return ErrorCategory.AUTHENTICATION
    }
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return ErrorCategory.NETWORK
    }
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return ErrorCategory.VALIDATION
    }
    return ErrorCategory.SYSTEM
  }

  private generateUserMessage(error: Error): string {
    if (error.message.includes('401')) {
      return 'Please log in to continue'
    }
    if (error.message.includes('403')) {
      return 'You do not have permission to perform this action'
    }
    if (error.message.includes('404')) {
      return 'The requested resource was not found'
    }
    if (error.message.includes('500')) {
      return 'A server error occurred. Please try again later'
    }
    if (error.message.includes('fetch')) {
      return 'Network error. Please check your connection'
    }
    return 'An unexpected error occurred'
  }

  private isActionable(error: Error): boolean {
    return error.message.includes('401') || 
           error.message.includes('403') || 
           error.message.includes('validation') ||
           error.message.includes('fetch')
  }

  private isRetryable(error: Error): boolean {
    return error.message.includes('fetch') || 
           error.message.includes('500') ||
           error.message.includes('timeout')
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private logError(error: StandardError): void {
    // Add to internal log
    this.errorLog.unshift(error)
    
    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
    
    // Console logging with appropriate level
    const logLevel = this.getLogLevel(error.severity)
    console[logLevel](`[${error.category.toUpperCase()}] ${error.message}`, {
      id: error.id,
      code: error.code,
      context: error.context,
      timestamp: error.timestamp
    })
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      case ErrorSeverity.LOW:
        return 'info'
      default:
        return 'error'
    }
  }

  private reportError(error: StandardError): void {
    // In production, send to monitoring service
    // This is a placeholder for actual error reporting
    if (error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH) {
      // Send to error tracking service (e.g., Sentry, LogRocket)
      console.info('Would report error to monitoring service:', error.id)
    }
  }

  private showUserMessage(error: StandardError): void {
    // Show user-friendly toast notification
    if (error.userMessage && typeof window !== 'undefined') {
      // This would integrate with your toast notification system
      console.info('User message:', error.userMessage)
    }
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(limit = 50): StandardError[] {
    return this.errorLog.slice(0, limit)
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = []
  }
}

/**
 * ARCHITECTURE FIX: Standardized error handling utilities
 */
export const errorHandler = StandardizedErrorHandler.getInstance()

export const handleApiError = (error: unknown, context?: Record<string, any>): StandardError => {
  if (error instanceof Error) {
    return errorHandler.handleError(error, context)
  }
  
  // Handle non-Error objects
  const genericError = new Error(String(error))
  return errorHandler.handleError(genericError, context)
}

export const withErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Record<string, any>
) => {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args)
    } catch (error) {
      handleApiError(error, context)
      return null
    }
  }
}
