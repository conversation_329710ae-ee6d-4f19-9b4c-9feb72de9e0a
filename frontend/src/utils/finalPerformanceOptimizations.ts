/**
 * PERFORMANCE FIX: Final Performance Optimizations
 * Ultimate performance enhancements for production-ready application
 */

import React, { useCallback, useMemo, useRef, useEffect, useState } from 'react'

// PERFORMANCE FIX: Advanced bundle optimization
export class BundleOptimizer {
  private static loadedChunks = new Set<string>()
  private static preloadedResources = new Set<string>()

  // Intelligent code splitting with preloading
  static async loadChunk(chunkName: string, preload = false): Promise<any> {
    if (this.loadedChunks.has(chunkName)) {
      return Promise.resolve()
    }

    const startTime = performance.now()

    try {
      let module
      switch (chunkName) {
        case 'employee-management':
          module = await import('../pages/EmployeeManagement')
          break
        case 'department-management':
          module = await import('../pages/DepartmentManagement')
          break
        case 'announcements':
          module = await import('../pages/Announcements')
          break
        case 'reports':
          module = await import('../pages/Reports')
          break
        default:
          throw new Error(`Unknown chunk: ${chunkName}`)
      }

      this.loadedChunks.add(chunkName)
      
      const loadTime = performance.now() - startTime
      if (loadTime > 1000) {
        console.warn(`Slow chunk load: ${chunkName} took ${loadTime.toFixed(2)}ms`)
      }

      return module
    } catch (error) {
      console.error(`Failed to load chunk ${chunkName}:`, error)
      throw error
    }
  }

  // PERFORMANCE FIX: Enhanced resource preloading with priority and error handling
  static preloadResource(url: string, type: 'script' | 'style' | 'font' | 'image' = 'script', priority: 'high' | 'low' = 'low') {
    if (this.preloadedResources.has(url)) return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    link.as = type

    // PERFORMANCE FIX: Set fetch priority for modern browsers
    if ('fetchPriority' in link) {
      (link as any).fetchPriority = priority
    }

    if (type === 'font') {
      link.crossOrigin = 'anonymous'
      // PERFORMANCE FIX: Add font-display swap for better performance
      link.setAttribute('font-display', 'swap')
    }

    // PERFORMANCE FIX: Add error handling and success tracking
    link.onerror = () => {
      console.warn(`Failed to preload resource: ${url}`)
      this.preloadedResources.delete(url)
    }

    link.onload = () => {
      console.log(`Successfully preloaded: ${url}`)
    }

    document.head.appendChild(link)
    this.preloadedResources.add(url)
  }

  // Optimize images with lazy loading and WebP support
  static optimizeImage(src: string, options: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'avif' | 'auto'
  } = {}): string {
    const { width, height, quality = 80, format = 'auto' } = options

    // Check WebP support
    const supportsWebP = document.createElement('canvas')
      .toDataURL('image/webp')
      .indexOf('data:image/webp') === 0

    // Build optimized URL (assuming image optimization service)
    const params = new URLSearchParams()
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    
    if (format === 'auto') {
      params.set('f', supportsWebP ? 'webp' : 'jpg')
    } else {
      params.set('f', format)
    }

    return `${src}?${params.toString()}`
  }
}

// PERFORMANCE FIX: Advanced React optimizations
export class ReactOptimizer {
  // Intelligent component memoization
  static memoizeComponent<P extends object>(
    Component: React.ComponentType<P>,
    areEqual?: (prevProps: P, nextProps: P) => boolean
  ) {
    const MemoizedComponent = React.memo(Component, areEqual)
    MemoizedComponent.displayName = `Memoized(${Component.displayName || Component.name})`
    return MemoizedComponent
  }

  // Deep comparison for complex props
  static deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true
    if (obj1 == null || obj2 == null) return false
    if (typeof obj1 !== typeof obj2) return false

    if (typeof obj1 === 'object') {
      const keys1 = Object.keys(obj1)
      const keys2 = Object.keys(obj2)
      
      if (keys1.length !== keys2.length) return false
      
      for (const key of keys1) {
        if (!keys2.includes(key)) return false
        if (!this.deepEqual(obj1[key], obj2[key])) return false
      }
      
      return true
    }

    return obj1 === obj2
  }

  // Optimized event handlers
  static createOptimizedHandler<T extends (...args: any[]) => void>(
    handler: T,
    dependencies: React.DependencyList
  ): T {
    return useCallback(handler, dependencies)
  }
}

// PERFORMANCE FIX: Advanced caching system
export class AdvancedCache {
  private static cache = new Map<string, {
    data: any
    timestamp: number
    ttl: number
    hits: number
  }>()
  
  private static maxSize = 100
  private static cleanupInterval: NodeJS.Timeout | null = null

  static {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000) // Cleanup every minute
  }

  // Set cache with TTL and LRU eviction
  static set(key: string, data: any, ttl = 300000): void { // 5 minutes default
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0][0]
      this.cache.delete(oldestKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0
    })
  }

  // Get from cache with hit tracking
  static get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    // Update hit count and timestamp for LRU
    entry.hits++
    entry.timestamp = Date.now()
    
    return entry.data
  }

  // Cleanup expired entries
  private static cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Get cache statistics
  static getStats() {
    const entries = Array.from(this.cache.values())
    return {
      size: this.cache.size,
      totalHits: entries.reduce((sum, entry) => sum + entry.hits, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length
        : 0
    }
  }

  // Clear cache
  static clear(): void {
    this.cache.clear()
  }
}

// PERFORMANCE FIX: Network optimization
export class NetworkOptimizer {
  private static requestQueue = new Map<string, Promise<any>>()
  private static retryAttempts = new Map<string, number>()

  // Request deduplication and batching
  static async optimizedFetch(
    url: string, 
    options: RequestInit = {},
    retryConfig = { maxRetries: 3, backoffMs: 1000 }
  ): Promise<Response> {
    const requestKey = `${options.method || 'GET'}:${url}:${JSON.stringify(options.body || {})}`

    // Check if request is already in flight
    if (this.requestQueue.has(requestKey)) {
      return this.requestQueue.get(requestKey)!
    }

    const requestPromise = this.executeRequest(url, options, retryConfig)
    this.requestQueue.set(requestKey, requestPromise)

    try {
      const response = await requestPromise
      return response
    } finally {
      // Clean up after request completes
      setTimeout(() => {
        this.requestQueue.delete(requestKey)
      }, 1000) // Keep for 1 second to handle rapid duplicate requests
    }
  }

  private static async executeRequest(
    url: string,
    options: RequestInit,
    retryConfig: { maxRetries: number; backoffMs: number }
  ): Promise<Response> {
    const requestKey = `${options.method || 'GET'}:${url}`
    const attempts = this.retryAttempts.get(requestKey) || 0

    try {
      const response = await fetch(url, {
        ...options,
        // Add performance optimizations
        keepalive: true,
        // Add compression
        headers: {
          'Accept-Encoding': 'gzip, deflate, br',
          ...options.headers
        }
      })

      // Reset retry count on success
      this.retryAttempts.delete(requestKey)
      
      return response
    } catch (error) {
      if (attempts < retryConfig.maxRetries) {
        this.retryAttempts.set(requestKey, attempts + 1)
        
        // Exponential backoff
        const delay = retryConfig.backoffMs * Math.pow(2, attempts)
        await new Promise(resolve => setTimeout(resolve, delay))
        
        return this.executeRequest(url, options, retryConfig)
      }

      // Clean up retry attempts
      this.retryAttempts.delete(requestKey)
      throw error
    }
  }

  // Batch multiple requests
  static async batchRequests<T>(
    requests: Array<() => Promise<T>>,
    batchSize = 5
  ): Promise<T[]> {
    const results: T[] = []
    
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(request => request()))
      results.push(...batchResults)
      
      // Small delay between batches to prevent overwhelming the server
      if (i + batchSize < requests.length) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
    
    return results
  }
}

// PERFORMANCE FIX: Advanced React hooks
export function useOptimizedState<T>(
  initialState: T | (() => T),
  equalityFn?: (prev: T, next: T) => boolean
): [T, (newState: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState(initialState)
  
  const optimizedSetState = useCallback((newState: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState
      
      // Use custom equality function or default comparison
      if (equalityFn ? equalityFn(prevState, nextState) : prevState === nextState) {
        return prevState // Prevent unnecessary re-render
      }
      
      return nextState
    })
  }, [equalityFn])
  
  return [state, optimizedSetState]
}

export function useDebounced<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value)
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}

export function useThrottled<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now())
  
  return useCallback((...args: Parameters<T>) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args)
      lastRun.current = Date.now()
    }
  }, [callback, delay]) as T
}

// PERFORMANCE FIX: Resource monitoring and cleanup
export class ResourceMonitor {
  private static resources = {
    intervals: new Set<NodeJS.Timeout>(),
    timeouts: new Set<NodeJS.Timeout>(),
    observers: new Set<any>(),
    eventListeners: new Map<EventTarget, Set<string>>()
  }

  static trackInterval(interval: NodeJS.Timeout): NodeJS.Timeout {
    this.resources.intervals.add(interval)
    return interval
  }

  static trackTimeout(timeout: NodeJS.Timeout): NodeJS.Timeout {
    this.resources.timeouts.add(timeout)
    return timeout
  }

  static trackObserver(observer: any): any {
    this.resources.observers.add(observer)
    return observer
  }

  static trackEventListener(target: EventTarget, type: string): void {
    if (!this.resources.eventListeners.has(target)) {
      this.resources.eventListeners.set(target, new Set())
    }
    this.resources.eventListeners.get(target)!.add(type)
  }

  static cleanup(): void {
    // Clear intervals
    this.resources.intervals.forEach(interval => clearInterval(interval))
    this.resources.intervals.clear()

    // Clear timeouts
    this.resources.timeouts.forEach(timeout => clearTimeout(timeout))
    this.resources.timeouts.clear()

    // Disconnect observers
    this.resources.observers.forEach(observer => {
      if (observer.disconnect) observer.disconnect()
      if (observer.unobserve) observer.unobserve()
    })
    this.resources.observers.clear()

    // Remove event listeners
    this.resources.eventListeners.clear()

    console.log('🧹 Resource cleanup completed')
  }

  static getResourceCount(): number {
    return this.resources.intervals.size + 
           this.resources.timeouts.size + 
           this.resources.observers.size + 
           Array.from(this.resources.eventListeners.values())
             .reduce((sum, set) => sum + set.size, 0)
  }
}

// Initialize cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    ResourceMonitor.cleanup()
    AdvancedCache.clear()
  })
}

export default {
  BundleOptimizer,
  ReactOptimizer,
  AdvancedCache,
  NetworkOptimizer,
  ResourceMonitor,
  useOptimizedState,
  useDebounced,
  useThrottled
}
